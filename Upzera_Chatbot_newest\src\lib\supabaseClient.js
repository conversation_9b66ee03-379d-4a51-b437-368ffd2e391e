import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseKey = import.meta.env.VITE_SUPABASE_KEY
const supabaseTable = import.meta.env.VITE_SUPABASE_TABLE

export const supabase = createClient(supabaseUrl, supabaseKey)

export async function saveLead(leadData) {
  console.log('Saving lead to table:', supabaseTable)
  console.log('Lead data:', leadData)
  
  try {
    const { error } = await supabase
      .from(supabaseTable)
      .insert(leadData, { returning: 'minimal' })

    if (error) {
      console.error('Supabase error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      })
      throw error
    }

    console.log('Successfully saved lead')
    return { success: true }
  } catch (error) {
    console.error('Full error object:', error)
    return { 
      error: {
        message: error.message,
        ...(error.details && { details: error.details }),
        ...(error.hint && { hint: error.hint })
      }
    }
  }
}
