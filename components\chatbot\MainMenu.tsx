import React from 'react';
import { ScreenSize } from './types';

interface MenuOption {
  id: string;
  icon: string;
  text: string;
  description: string;
  message: string;
  intent: string;
}

interface MainMenuProps {
  onMenuClick: (message: string, intent: string) => void;
  screenSize: ScreenSize;
}

export default function MainMenu({ onMenuClick, screenSize }: MainMenuProps) {
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;
  
  // Adjust sizing based on screen size
  let buttonPadding = 'px-6 py-4';
  let buttonText = 'text-base';
  let spacing = 'gap-3';
  let titleText = 'text-lg';
  
  if (isSmall) {
    if (width <= 320) {
      buttonPadding = 'px-3 py-3';
      buttonText = 'text-sm';
      spacing = 'gap-2';
      titleText = 'text-base';
    } else if (width <= 375) {
      buttonPadding = 'px-4 py-3';
      buttonText = 'text-sm';
      spacing = 'gap-2';
      titleText = 'text-base';
    } else {
      buttonPadding = 'px-5 py-3';
      buttonText = 'text-base';
      spacing = 'gap-3';
      titleText = 'text-lg';
    }
  }

  const menuOptions: MenuOption[] = [
    {
      id: 'prices',
      icon: '💰',
      text: 'PRICES',
      description: 'View our pricing and packages',
      message: 'I want to know about your prices',
      intent: 'prices'
    },
    {
      id: 'booking',
      icon: '📅',
      text: 'BOOK MEETING',
      description: 'Schedule a free consultation',
      message: 'I want to book a meeting',
      intent: 'booking'
    },
    {
      id: 'services',
      icon: '🚀',
      text: 'SERVICES',
      description: 'Learn about our solutions',
      message: 'Tell me about your services',
      intent: 'services'
    }
  ];

  const handleMenuClick = (option: MenuOption) => {
    onMenuClick(option.message, option.intent);
  };

  return (
    <div className="w-full animate-fade-in mb-4">
      <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-100">
        <h3 className={`${titleText} font-bold text-gray-800 mb-4 text-center`}>
          How can I help you today?
        </h3>
        <div className={`space-y-3 ${spacing}`}>
          {menuOptions.map((option) => (
            <button
              key={option.id}
              onClick={() => handleMenuClick(option)}
              className={`
                w-full ${buttonPadding}
                bg-white border-2 border-gray-200 rounded-lg
                hover:border-blue-400 hover:bg-blue-50
                transition-all duration-200
                text-left
                shadow-sm hover:shadow-md
                group
                transform hover:scale-[1.02]
              `}
            >
              <div className="flex items-center space-x-4">
                <span className="text-3xl flex-shrink-0 group-hover:scale-110 transition-transform duration-200">
                  {option.icon}
                </span>
                <div className="flex-1 min-w-0">
                  <h4 className={`${buttonText} font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-200`}>
                    {option.text}
                  </h4>
                  <p className={`text-sm text-gray-600 mt-1`}>
                    {option.description}
                  </p>
                </div>
              </div>
            </button>
          ))}
        </div>
        
        <div className="mt-4 pt-3 border-t border-gray-200">
          <button
            onClick={() => onMenuClick('I need to speak with a human', 'human_help')}
            className={`
              w-full ${buttonPadding}
              bg-gradient-to-r from-gray-600 to-gray-700
              text-white rounded-lg font-medium
              hover:from-gray-700 hover:to-gray-800
              transition-all duration-200
              shadow-md hover:shadow-lg
              transform hover:scale-[1.02]
              ${buttonText}
            `}
          >
            💬 Speak with a Human
          </button>
        </div>
      </div>
    </div>
  );
}
