import React from 'react';
import { ScreenSize } from './types';

interface Service {
  id: string;
  icon: string;
  title: string;
  description: string;
  price: string;
  message: string;
}

interface ServiceOptionsProps {
  onServiceClick: (message: string) => void;
  screenSize: ScreenSize;
}

export default function ServiceOptions({ onServiceClick, screenSize }: ServiceOptionsProps) {
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;
  
  // Adjust sizing based on screen size
  let buttonPadding = 'px-4 py-3';
  let buttonText = 'text-sm';
  let spacing = 'gap-3';
  let titleText = 'text-base';
  let priceText = 'text-xs';
  
  if (isSmall) {
    if (width <= 320) {
      buttonPadding = 'px-2 py-2';
      buttonText = 'text-xs';
      spacing = 'gap-2';
      titleText = 'text-sm';
      priceText = 'text-xs';
    } else if (width <= 375) {
      buttonPadding = 'px-3 py-2.5';
      buttonText = 'text-xs';
      spacing = 'gap-2';
      titleText = 'text-sm';
      priceText = 'text-xs';
    } else {
      buttonPadding = 'px-3 py-3';
      buttonText = 'text-sm';
      spacing = 'gap-2.5';
      titleText = 'text-base';
      priceText = 'text-xs';
    }
  }

  const services: Service[] = [
    {
      id: 'web-development',
      icon: '🚀',
      title: 'Full-Stack Web Development',
      description: 'Custom websites and web applications that look great and work even better.',
      price: 'Starting from €350',
      message: 'Tell me about your web development services'
    },
    {
      id: 'chatbots',
      icon: '🤖',
      title: 'Chatbots & Lead Generation',
      description: 'AI-powered chat solutions that work 24/7 to capture and qualify leads.',
      price: 'Starting from €500',
      message: 'I want to know about your chatbot services'
    },
    {
      id: 'support',
      icon: '🛠️',
      title: 'Ongoing Support',
      description: 'Continuous maintenance, updates, and improvements for your solutions.',
      price: 'Custom pricing',
      message: 'What kind of ongoing support do you provide?'
    }
  ];

  const handleServiceClick = (service: Service) => {
    onServiceClick(service.message);
  };

  return (
    <div className="w-full animate-fade-in mb-4">
      <div className="bg-gray-50 rounded-lg p-3">
        <p className={`text-gray-600 mb-3 ${buttonText} text-center font-medium`}>
          Choose a service to learn more:
        </p>
        <div className={`space-y-3 ${spacing}`}>
          {services.map((service) => (
            <button
              key={service.id}
              onClick={() => handleServiceClick(service)}
              className={`
                w-full ${buttonPadding}
                bg-white border border-gray-200 rounded-lg
                hover:border-pink-300 hover:bg-pink-50
                transition-all duration-200
                text-left
                shadow-sm hover:shadow-md
                group
              `}
            >
              <div className="flex items-start space-x-3">
                <span className="text-2xl flex-shrink-0 group-hover:scale-110 transition-transform duration-200">
                  {service.icon}
                </span>
                <div className="flex-1 min-w-0">
                  <h3 className={`${titleText} font-semibold text-gray-800 group-hover:text-pink-600 transition-colors duration-200`}>
                    {service.title}
                  </h3>
                  <p className={`${buttonText} text-gray-600 mt-1 leading-relaxed`}>
                    {service.description}
                  </p>
                  <p className={`${priceText} text-pink-600 font-medium mt-2`}>
                    {service.price}
                  </p>
                </div>
              </div>
            </button>
          ))}
        </div>
        <div className="mt-4 text-center">
          <button
            onClick={() => onServiceClick('I want to schedule a consultation to discuss my project')}
            className={`
              ${buttonPadding} ${buttonText}
              bg-gradient-to-r from-pink-500 to-purple-600
              text-white rounded-lg font-medium
              hover:from-pink-600 hover:to-purple-700
              transition-all duration-200
              shadow-md hover:shadow-lg
              transform hover:scale-105
              w-full
            `}
          >
            💬 Discuss Your Project - Free Consultation
          </button>
        </div>
      </div>
    </div>
  );
}
