@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --chat-font-size: 16px;
  --chat-padding: 16px;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Smooth scrolling for chat container */
.overflow-y-auto {
  scroll-behavior: smooth;
}

/* Force remove border from chatbot container */
.fixed.bottom-6.left-6.z-\\[9999\\] > div {
  border: none !important;
}

/* Responsive classes */
.responsive-text {
  font-size: var(--chat-font-size);
}

.responsive-padding {
  padding: var(--chat-padding);
}

/* Hide non-essential elements on small screens */
@media (max-width: 375px) {
  .non-essential {
    display: none;
  }
}

/* Ensure chatbot styles are properly applied */
.chatbot-container {
  position: fixed !important;
  bottom: 1.5rem !important;
  left: 1.5rem !important;
  z-index: 9999 !important;
}

/* Ensure gradient backgrounds are applied */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important;
}

.from-pink-500 {
  --tw-gradient-from: #ec4899 !important;
  --tw-gradient-to: rgb(236 72 153 / 0) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.to-purple-600 {
  --tw-gradient-to: #9333ea !important;
}
