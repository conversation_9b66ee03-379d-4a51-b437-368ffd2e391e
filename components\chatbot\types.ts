// Chatbot type definitions
export interface Message {
  text: string;
  isUser: boolean;
  type?: string;
}

export interface ScreenSize {
  width: number;
  height: number;
  isSmall: boolean;
}

export interface UserContext {
  name: string;
  email: string;
  lastIntent: string | null;
  selectedService: string | null;
  awaitingResponse: boolean;
}

export interface LeadFormData {
  name: string;
  email: string;
  isSubmitting: boolean;
  error: string | null;
}

export interface IntentMatch {
  intent: string;
  confidence: number;
  originalMessage: string;
}

export interface IntentResponse {
  type: string;
  messages: (string | { type: string })[];
  endConversation?: boolean;
}

export interface ConversationContext {
  lastBotMessage: string;
  messageCount: number;
  hasShownCalendly: boolean;
}

export interface CompanyKnowledge {
  company_info?: {
    name?: string;
    mission?: string;
    location?: string;
    tagline?: string;
    values?: string[];
    founding_year?: number;
    address?: string;
  };
  services?: Array<{
    name: string;
    description: string;
    keywords: string[];
    service_tiers?: Array<{
      price_range?: string;
    }>;
  }>;
  contact_info?: {
    email?: string;
    phone?: string;
    calendly_url?: string;
  };
  faqs?: Array<{
    question: string;
    answer: string;
    keywords: string[];
  }>;
  conversation_flows?: {
    greetings?: string[];
  };
}
