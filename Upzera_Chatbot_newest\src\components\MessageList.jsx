import React, { useState, useEffect, forwardRef } from 'react';
import ChatMessage from './ChatMessage';

const MessageList = forwardRef(({ messages, isBotTyping, screenSize, onQuickAction }, ref) => {
  const [showCalendly, setShowCalendly] = useState(false);
  
  // Get screen size properties
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;
  
  // Adjust padding based on screen size
  let containerPadding = 'px-4 py-2';
  
  if (isSmall) {
    if (width <= 320) {
      containerPadding = 'px-2 py-1';
    } else if (width <= 375) {
      containerPadding = 'px-3 py-1.5';
    } else {
      containerPadding = 'px-3 py-2';
    }
  }

  useEffect(() => {
    if (
      messages.some(msg =>
        /schedule|book|meeting|calendar|appointment/i.test(msg.text)
      )
    ) {
      setShowCalendly(true);
    }
  }, [messages]);

  // Scroll to the bottom whenever messages change
  useEffect(() => {
    if (ref && ref.current) {
      ref.current.scrollTop = ref.current.scrollHeight;
    }
  }, [messages, isBotTyping, ref]);

  return (
    <div ref={ref} className={`flex-1 overflow-y-auto ${containerPadding}`}>
      {messages.map((message, index) => (
        <ChatMessage
          key={index}
          message={message}
          screenSize={screenSize}
          onQuickAction={onQuickAction}
        />
      ))}
      {isBotTyping && (
        <ChatMessage 
          message={{ text: '...', isUser: false }} 
          screenSize={screenSize} 
        />
      )}
    </div>
  );
});

MessageList.displayName = 'MessageList';

export default MessageList;