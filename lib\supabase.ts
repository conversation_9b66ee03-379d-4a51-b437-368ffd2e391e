import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  // Create a dummy client to prevent errors during build
  const dummyClient = createClient('https://dummy.supabase.co', 'dummy-key');
  export { dummyClient as supabase };
  export const saveLead = async () => ({ error: 'Supabase not configured' });
} else {

  export const supabase = createClient(supabaseUrl, supabaseKey);

  // Lead saving function
  export const saveLead = async (leadData: {
    name: string;
    email: string;
    created_at: string;
  }) => {
    try {
      const tableName = process.env.NEXT_PUBLIC_SUPABASE_TABLE || 'leads';
      const { data, error } = await supabase
        .from(tableName)
        .insert([leadData]);

      if (error) {
        console.error('Supabase error:', error);
        return { error };
      }

      return { data };
    } catch (error) {
      console.error('Error saving lead:', error);
      return { error };
    }
  };
}
