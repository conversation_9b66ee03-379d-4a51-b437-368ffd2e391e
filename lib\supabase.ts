import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseKey);

// Lead saving function
export const saveLead = async (leadData: {
  name: string;
  email: string;
  created_at: string;
}) => {
  try {
    const tableName = process.env.NEXT_PUBLIC_SUPABASE_TABLE || 'leads';
    const { data, error } = await supabase
      .from(tableName)
      .insert([leadData]);

    if (error) {
      console.error('Supabase error:', error);
      return { error };
    }

    return { data };
  } catch (error) {
    console.error('Error saving lead:', error);
    return { error };
  }
};
