import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_KEY;

// Create Supabase client with fallback for missing environment variables
export const supabase = createClient(
  supabaseUrl || 'https://dummy.supabase.co',
  supabaseKey || 'dummy-key'
);

// Lead saving function
export const saveLead = async (leadData: {
  name: string;
  email: string;
  created_at: string;
}) => {
  // Check if environment variables are properly configured
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase environment variables. Please check your .env.local file.');
    return { error: 'Supabase not configured properly' };
  }

  try {
    const tableName = process.env.NEXT_PUBLIC_SUPABASE_TABLE || 'leads';
    const { data, error } = await supabase
      .from(tableName)
      .insert([leadData]);

    if (error) {
      console.error('Supabase error:', error);
      return { error };
    }

    return { data };
  } catch (error) {
    console.error('Error saving lead:', error);
    return { error };
  }
};
